<?php
    session_start();
?>

<html>
    <head>
        <title>Project 3</title>
    </head>

    <body>
        <?php
            if (isset($_POST["login"]))
            {
                $user = $_POST["user"];
                $passwd = $_POST["passwd"];

                if ($user == "admin" && $passwd == "admin")
                {
                    $_SESSION["user"] = $user;
                    $_SESSION["logged_in"] = true;
                    $_SESSION["password"] = $passwd; // store the current password

                    if (!isset($_SESSION["balance"]))
                    {
                        $_SESSION["balance"] = 0;
                    }
                    if (!isset($_SESSION["transactions"]))
                    {
                        $_SESSION["transactions"] = array();
                    }
                }
                else
                {
                    echo "Invalid username or password! Please try again. <br>";
                    echo "<a href='Project3.php'>Back to Login</a>";
                    exit();
                }
            }

            if (!isset($_SESSION["logged_in"]) || $_SESSION["logged_in"] != true)
            {
                echo "You must be logged in to access this page. <br>";
                echo "<a href='Project3.php'>Back to Login</a>";
                exit();
            }


            $user = $_SESSION["user"];
            $message = "";

            if (isset($_POST["submit"]) && isset($_POST["action"]))
            {
                $action = $_POST["action"];

                // Show Balance
                if ($action == "showBalance")
                {
                    echo "<h3>Balance Info:</h3> <br>";
                    $message = "Dear customer, your balance is: $" . number_format($_SESSION["balance"], 2);
                }
                // Deposit
                elseif ($action == "deposit")
                {
                    $amount = floatval($_POST["depositAmount"]);
                    if ($amount > 0) {
                        $_SESSION["balance"] += $amount;
                        $transaction = array(
                            'type' => 'Deposit',
                            'amount' => $amount,
                            'date' => date("M/d/Y"),
                            'time' => date("h:i:sA"),
                            'ip' => $_SERVER['REMOTE_ADDR']
                        );
                        $_SESSION["transactions"][] = $transaction;
                        $message = "Thank you for deposit of $".number_format($amount, 2)." <br> 
                                    Your new balance now is : $" . number_format($_SESSION["balance"], 2);
                    } else {
                        echo "<h2>Deposit Info:</h2> <br>";
                        $message = "The deposit amount must be greater than $0.00. Please try again.";
                    }
                }
                // Withdraw
                elseif ($action == "withdraw") {
                    $amount = floatval($_POST["withdrawAmount"]);
                    if ($amount > 0) {
                        if ($amount <= $_SESSION["balance"]) {
                            $_SESSION["balance"] -= $amount;
                            $transaction = array(
                                'type' => 'Withdrawal',
                                'amount' => $amount,
                                'date' => date("M/d/Y"),
                                'time' => date("h:i:sA"),
                                'ip' => $_SERVER['REMOTE_ADDR']
                            );
                            $_SESSION["transactions"][] = $transaction;
                            echo "<h2>Withdraw Info:</h2> <br>";
                            $message = "Successfully withdrew $". number_format($amount, 2)." <br>
                                        New balance: $" . number_format($_SESSION["balance"], 2);
                        } else {
                            $message = "Insufficient funds. Your current balance is $" . number_format($_SESSION["balance"], 2);
                        }
                    } else {
                        $message = "Withdrawal amount must be greater than $0.00";
                    }
                }
                // Show Transactions
                elseif ($action == "tranAction")
                {
                    if (empty($_SESSION["transactions"])) {
                        $message = "No transactions found for this session.";
                    } else {
                        $message = "<h3>Transaction info:</h3> <br>";
                        $counter = 1;
                        foreach ($_SESSION["transactions"] as $transaction)
                        {
                            if (is_array($transaction)) {
                                // New format
                                $message .= $counter . ". " . $transaction['type'] . ": $" . number_format($transaction['amount'], 0) .
                                           ", on " . $transaction['date'] . ", at " . $transaction['time'] .
                                           ", from " . $transaction['ip'] . "<br>";
                            } else {
                                // Old format (for backward compatibility)
                                $message .= $counter . ". " . $transaction . "<br>";
                            }
                            $counter++;
                        }
                    }
                }
                // Change Password
                elseif ($action == "changePass")
                {
                    $oldPass = $_POST["oldPassword"];
                    $newPass = $_POST["newPassword"];
                    $retypePass = $_POST["retypePassword"];

                    if ($oldPass == $_SESSION["password"])
                    {
                        if ($newPass == $retypePass && !empty($newPass))
                        {
                            $_SESSION["password"] = $newPass;
                            $message = "Password successfully changed!";
                        } else {
                            $message = "New passwords do not match or are empty!";
                        }
                    } else {
                        $message = "Old password is incorrect!";
                    }
                }
                // Logout
                elseif ($action == "logout")
                {
                    echo "<div style='background-color:pink; border:red solid 1px; width:75%; margin:auto;'>";
                    echo "<h3>Logout Info</h3>";
                    echo "You logged out sucessfully! <br>";
                    echo "Please click <a href='Project3.php'>here</a> to login again. <br>";
                    echo "Just a friendly reminder that this is a fake bank system.When you log out, your deposit will be reset to 0 and all your transactions will be cleared. <br>";
                    echo "</div>";
                    session_destroy();
                    exit();
                }
            }

            echo "<div style='background-color:pink; border:red solid 1px; width:75%; margin:auto;'>";
            echo "Welcome ".$user."<br>";
            date_default_timezone_set("America/New_York");
            $now = time();
            echo "Now is ".date("F j, Y", $now)." ".date("h:i:s A", $now)." <br>";
            echo "</div>";
            echo "<hr>";

            if (!empty($message)) {
                echo "<div style='background-color:lightyellow; border:red solid 1px; width:72%; margin:auto; padding: 10px'>";
                echo $message;
                echo "</div>";
                echo "<hr>";
            }

            echo "<div style='background-color:lightblue; border:red solid 1px; width:75%; margin:auto;'>";
            echo "Choose what you want to do based on the following menu: <br>";
            echo "<hr>";
            echo "<form method='post' action='".$_SERVER['PHP_SELF']."'>";
            echo "<input type='radio' name='action' value='showBalance' required> Show Balance <br>";
            echo "<input type='radio' name='action' value='deposit' required> Deposit this amount: <input type='text' name='depositAmount' placeholder='0.00'> <br>";
            echo "<input type='radio' name='action' value='withdraw' required> Withdraw this amount: <input type='text' name='withdrawAmount' placeholder='0.00'> <br>";
            echo "<input type='radio' name='action' value='tranAction' required> Show my transactions <br>";
            echo "<input type='radio' name='action' value='changePass' required> Change my password <br>";
            echo "Old Password: <input type='password' name='oldPassword'><br>";
            echo "New Password: <input type='password' name='newPassword'><br>";
            echo "Re-type New Password: <input type='password' name='retypePassword'><br><br>";
            echo "<input type='radio' name='action' value='logout' required> Log Out <br>";
            echo "<hr>";
            echo "<input type='submit' name='submit' value='Submit'>";
            echo "</form>";
            echo "</div>";
        ?>
    </body>
</html>